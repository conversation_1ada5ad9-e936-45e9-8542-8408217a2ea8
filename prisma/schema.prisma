generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  userId       String
  expires      DateTime
  sessionToken String   @unique
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model User {
  id             String         @id @default(cuid())
  email          String?        @unique
  name           String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  googleId       String?        @unique
  picture        String?
  authMethod     String?
  displayName    String?
  avatarDataUrl  String?
  emailVerified  DateTime?
  image          String?
  hashedPassword String?
  derivAccountId String?        @unique // This is the Deriv main login ID, might be CR... or VRTC...
  provider       String?
  accounts       Account[]
  apiKeys        ApiKey[]
  notifications  Notification[]
  alerts         PriceAlert[]
  profitSummary  ProfitSummary?
  savedItems     SavedItem[]
  sessions       Session[]
  trades         Trade[]
  usageStats     UsageStats[]
  settings       UserSettings?
  watchlists     Watchlist[]
}

model UserSettings {
  id                        String    @id @default(uuid())
  userId                    String    @unique
  user                      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  theme                     String    @default("light")
  language                  String    @default("en")
  notifications             Boolean   @default(true)
  settings                  Json      // For other dynamic user-specific settings

  // Deriv-specific settings
  derivDemoAccountId        String?   // e.g., "VRTC..."
  derivRealAccountId        String?   // e.g., "CR..."
  selectedDerivAccountType  String?   @default("demo") // "demo" or "real"
  derivDemoBalance          Float?
  derivRealBalance          Float?
  lastBalanceSync           DateTime? // Timestamp of the last successful balance update
  derivRealApiToken         String?   // New field
  derivDemoApiToken         String?   // New field

  createdAt                 DateTime  @default(now())
  updatedAt                 DateTime  @updatedAt
}

model UsageStats {
  id        String   @id @default(uuid())
  userId    String
  action    String
  timestamp DateTime @default(now())
  metadata  Json?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([timestamp])
}

model SavedItem {
  id        String   @id @default(uuid())
  userId    String
  title     String
  content   String
  url       String?
  tags      String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tags])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  title     String
  message   String
  type      String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([read])
}

model Trade {
  id                String    @id @default(uuid())
  userId            String
  symbol            String

  // New Deriv-style fields
  tradeType         String?   // e.g., 'Higher/Lower', 'Even/Odd', 'Over/Under', 'Rise/Fall', 'Touch/No Touch'
  entryPrice        Float?    // Price when trade began
  exitPrice         Float?    // Price when trade concluded
  buyPrice          Float?    // Price of the contract (stake amount)
  profitLoss        Float?    // Profit/Loss after trade completion

  // Legacy fields (kept for backward compatibility)
  type              String    // e.g., 'CALL', 'PUT', 'BUY', 'SELL'
  amount            Float     // Stake or investment amount
  price             Float     // Entry price
  totalValue        Float     // For binary options, this is typically the stake amount
  profit            Float?    // Legacy profit field

  status            String    // e.g., 'OPEN', 'WON', 'LOST', 'CLOSED'
  openTime          DateTime  @default(now())
  closeTime         DateTime?
  metadata          Json?     // For AI reasoning, selected strategy, other trade parameters
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  exchange          String?   // Exchange if applicable (e.g., for crypto, stocks)
  fees              Float?    // Trading fees
  leverage          Float?    // Leverage used, if any
  orderType         String?   // e.g., 'MARKET', 'LIMIT', 'STOP'
  stopLoss          Float?    // Stop-loss price level
  takeProfit        Float?    // Take-profit price level

  aiStrategyId      String?   // Identifier for the AI strategy used, if any

  // Deriv specific fields
  derivContractId   String?   // Deriv's contract ID for the trade
  derivAccountId    String?   // The specific Deriv account ID (CR... or VRTC...) used for the trade
  accountType       String?   // 'demo' or 'real', indicating which account type was used

  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbol])
  @@index([status])
  @@index([openTime])
  @@index([derivContractId])
  @@index([derivAccountId])
}

model ProfitSummary {
  id            String   @id @default(uuid())
  userId        String   @unique
  totalProfit   Float    @default(0)
  totalTrades   Int      @default(0)
  winningTrades Int      @default(0)
  losingTrades  Int      @default(0)
  winRate       Float    @default(0)
  lastUpdated   DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Watchlist {
  id        String   @id @default(uuid())
  userId    String
  name      String
  symbols   String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbols])
}

model PriceAlert {
  id        String   @id @default(uuid())
  userId    String
  symbol    String
  price     Float
  condition String
  triggered Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([symbol])
  @@index([triggered])
}

model ApiKey {
  id         String   @id @default(uuid())
  userId     String
  exchange   String
  apiKey     String
  secretKey  String
  passphrase String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([exchange])
}

model ExtensionLog {
  id        String   @id @default(uuid())
  level     String
  message   String
  metadata  Json?
  timestamp DateTime @default(now())

  @@index([level])
  @@index([timestamp])
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([token])
}
